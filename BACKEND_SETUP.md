# S3 Backend Setup Guide

This guide explains how to set up the S3 backend for Terraform state management.

## Overview

This Terraform project uses:
- **S3 bucket** for remote state storage
- **DynamoDB table** for state locking
- **Server-side encryption** for security
- **Versioning** for state file history

## Initial Setup (Bootstrap Process)

Since the S3 bucket and DynamoDB table are created by Terraform itself, you need to bootstrap the backend:

### Step 1: Comment Out Backend Configuration

1. Open `provider.tf`
2. Comment out the backend configuration:

```hcl
terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }

  # Comment out this block for initial setup
  # backend "s3" {
  #   bucket       = "my-terraform-state-ireland-unique"
  #   key          = "terraform.tfstate"
  #   region       = "eu-west-1"
  #   encrypt      = true
  #   use_lockfile = true
  # }
}
```

### Step 2: Configure Variables

1. Copy the example variables file:
   ```bash
   cp terraform.tfvars.example terraform.tfvars
   ```

2. Edit `terraform.tfvars` and update:
   - `aws_region` (should match your desired region)
   - Bucket name in both `provider.tf` and `backend.tf` (must be globally unique)
   - Other variables as needed

### Step 3: Initial Deployment

```bash
# Initialize Terraform (local state)
terraform init

# Plan the deployment
terraform plan

# Apply to create S3 bucket and DynamoDB table
terraform apply
```

### Step 4: Enable Backend Configuration

1. Uncomment the backend configuration in `provider.tf`
2. Update the bucket name if you changed it
3. Reinitialize Terraform with the backend:

```bash
# Reinitialize with S3 backend
terraform init

# Terraform will ask if you want to migrate state to S3
# Answer 'yes' to migrate the local state to S3
```

### Step 5: Verify Backend Setup

```bash
# Verify the configuration
terraform plan

# The state should now be stored in S3
```

## Important Notes

### Bucket Name Requirements
- Must be globally unique across all AWS accounts
- Must be DNS-compliant (lowercase, no underscores)
- Recommended format: `your-org-terraform-state-region-unique-id`

### Security Considerations
- The S3 bucket has public access blocked
- Server-side encryption is enabled
- DynamoDB table has encryption at rest
- State locking prevents concurrent modifications

### Customization Options

You can customize the backend by modifying these variables in `terraform.tfvars`:

```hcl
# DynamoDB table name for state locking
dynamodb_table_name = "my-custom-terraform-lock"

# DynamoDB billing mode
billing_mode = "PAY_PER_REQUEST"  # or "PROVISIONED"

# Common tags applied to all resources
common_tags = {
  Terraform   = "true"
  Owner       = "Your Team"
  Environment = "production"
  Project     = "eks-infrastructure"
}
```

## Troubleshooting

### Backend Already Exists Error
If you get an error about the backend already existing:
1. Check if the S3 bucket already exists
2. Verify the bucket name is correct in both files
3. Ensure you have proper AWS permissions

### State Lock Error
If you encounter state locking issues:
1. Check DynamoDB table exists and is accessible
2. Verify the table name matches the configuration
3. Ensure proper IAM permissions for DynamoDB

### Migration Issues
If state migration fails:
1. Backup your local `terraform.tfstate` file
2. Try `terraform init -migrate-state`
3. If needed, manually copy state using `terraform state pull/push`

## Cleanup

To remove the backend infrastructure:
1. Comment out the backend configuration
2. Run `terraform init` to use local state
3. Run `terraform destroy` to remove all resources
4. Manually delete the S3 bucket if needed (after emptying it)
