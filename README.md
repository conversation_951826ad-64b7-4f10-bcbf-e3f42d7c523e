# EKS Main VPC Terraform Project

This Terraform project creates a VPC with 7 subnets designed for EKS and Transit Gateway integration using a modular structure.

## Project Structure

```
eks-main/
├── provider.tf                             # Terraform and AWS provider configuration with S3 backend
├── variables.tf                            # Variable definitions
├── outputs.tf                             # Output definitions
├── terraform.tfvars.example               # Example variables file
├── backend.tf                             # S3 backend and DynamoDB state locking resources
├── vpc.tf                                 # VPC, Internet Gateway, Security Groups
├── subnets.tf                             # General use subnets and routing
├── transit_gateway_attachment.tf          # TGW attachment subnets and routing
├── README.md                              # This file
└── .gitignore                             # Git ignore rules
```

## Architecture Overview

- **VPC CIDR**: **********/16
- **Total Subnets**: 7
  - **2 Transit Gateway Attachment Subnets**: For connecting to Transit Gateway
  - **5 General Use Subnets**: For EKS nodes, applications, and other resources

## Resources Created

### Core Networking (vpc.tf)
- 1 VPC (**********/16)
- 1 Internet Gateway
- 1 Security Group for general use

### General Use Subnets (subnets.tf)
- 5 General use subnets across multiple Availability Zones
- 1 Route Table for general use subnets (with internet gateway route)
- Route Table Associations for general use subnets

### Transit Gateway Attachment (transit_gateway_attachment.tf)
- 2 Transit Gateway attachment subnets with specific names:
  - TGW-ECOMM-DEV-1a (eu-west-1a)
  - TGW-ECOMM-DEV-1b (eu-west-1c)
- 2 Individual Route Tables (one per subnet with matching names)
- Route Table Associations for each TGW attachment subnet
- Transit Gateway VPC attachment to existing shared Transit Gateway
- Optional routing configuration for TGW traffic

### Backend Infrastructure (backend.tf)
- S3 bucket for Terraform state storage with versioning and encryption
- S3 bucket public access block for security
- DynamoDB table for Terraform state locking with encryption

### Subnet Layout
| Subnet Name | CIDR Block | Availability Zone | Purpose |
|-------------|------------|-------------------|---------|
| TGW-ECOMM-DEV-1a | **********/24 | eu-west-1a | Transit Gateway Attachment |
| TGW-ECOMM-DEV-1b | 10.230.2.0/24 | eu-west-1c | Transit Gateway Attachment |
| General Subnet 1 | ***********/24 | eu-west-1a | EKS Nodes/Applications |
| General Subnet 2 | 10.230.11.0/24 | eu-west-1b | EKS Nodes/Applications |
| General Subnet 3 | 10.230.12.0/24 | eu-west-1c | EKS Nodes/Applications |
| General Subnet 4 | 10.230.13.0/24 | eu-west-1a | EKS Nodes/Applications |
| General Subnet 5 | 10.230.14.0/24 | eu-west-1b | EKS Nodes/Applications |

## Prerequisites

1. **Terraform**: Version >= 1.0
2. **AWS CLI**: Configured with appropriate credentials
3. **AWS Provider**: Version ~> 5.0

## Transit Gateway Configuration

This project is designed to connect to an **existing Transit Gateway** that was created manually (not through Terraform). The configuration includes:

- **VPC Attachment**: Connects your VPC to the existing Transit Gateway
- **Dedicated Subnets**: Uses the 2 TGW attachment subnets for the connection
- **DNS Support**: Configurable DNS resolution through the Transit Gateway
- **Routing**: Optional routing configuration for TGW traffic

### Important Notes:
- You must provide the existing Transit Gateway ID in `terraform.tfvars`
- The Transit Gateway must be in the same AWS region as your VPC
- Ensure your AWS account has permissions to create VPC attachments to the Transit Gateway
- The attachment subnets should be in different Availability Zones for high availability

## S3 Backend Configuration

This project is configured to use an S3 backend for remote state storage with DynamoDB for state locking. The backend configuration includes:

- **S3 Bucket**: `my-terraform-state-ireland-unique` (eu-west-1)
- **State File**: `terraform.tfstate`
- **Encryption**: Enabled
- **DynamoDB Table**: `terraform-state-lock` for state locking

### Important Notes:
- The S3 bucket and DynamoDB table are created as part of this Terraform configuration
- For the first run, you may need to comment out the backend configuration, apply the resources, then uncomment and migrate the state
- The bucket name should be globally unique - modify it in both `provider.tf` and `backend.tf`

## Usage

### 1. Clone and Setup
```bash
# Navigate to the project directory
cd eks-main

# Copy the example variables file
cp terraform.tfvars.example terraform.tfvars

# Edit terraform.tfvars with your specific values
# Modify aws_region, project_name, environment, etc.
```

### 2. Initialize Terraform
```bash
terraform init
```

### 3. Plan the Deployment
```bash
terraform plan
```

### 4. Apply the Configuration
```bash
terraform apply
```

### 5. Destroy (when needed)
```bash
terraform destroy
```

## Configuration

### Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `aws_region` | AWS region for resources | `us-west-2` | No |
| `project_name` | Name of the project | `eks-main` | No |
| `environment` | Environment name | `dev` | No |
| `vpc_cidr` | CIDR block for VPC | `**********/16` | No |
| `tgw_subnet_cidrs` | CIDR blocks for TGW subnets | See variables.tf | No |
| `general_subnet_cidrs` | CIDR blocks for general subnets | See variables.tf | No |
| `map_public_ip_on_launch` | Auto-assign public IP | `true` | No |
| `iam_user_access_key_id` | IAM access key for local testing | `null` | No |
| `iam_user_secret_access_key` | IAM secret key for local testing | `null` | No |
| `common_tags` | Common tags for all resources | See variables.tf | No |
| `dynamodb_table_name` | DynamoDB table name for state locking | `terraform-state-lock` | No |
| `billing_mode` | DynamoDB billing mode | `PAY_PER_REQUEST` | No |
| `existing_transit_gateway_id` | ID of existing Transit Gateway | `null` | Yes (if using TGW) |
| `create_transit_gateway_attachment` | Create TGW VPC attachment | `true` | No |
| `enable_dns_support_attachment` | Enable DNS support for TGW attachment | `true` | No |
| `enable_ipv6_support_attachment` | Enable IPv6 support for TGW attachment | `false` | No |
| `appliance_mode_support` | Enable appliance mode for TGW attachment | `disable` | No |

### Customization

To customize the subnet CIDRs, modify the `terraform.tfvars` file:

```hcl
# Example: Different subnet layout
tgw_subnet_cidrs = [
  "**********/24",
  "**********/24"
]

general_subnet_cidrs = [
  "***********/24",
  "***********/24",
  "***********/24",
  "***********/24",
  "***********/24"
]
```

## Outputs

The following outputs are available after deployment:

- `vpc_id`: VPC ID
- `tgw_attachment_subnet_ids`: Transit Gateway subnet IDs
- `general_use_subnet_ids`: General use subnet IDs
- `all_subnet_ids`: All subnet IDs combined
- And more... (see outputs.tf)

## Next Steps

After deploying this VPC infrastructure, you can:

1. **Deploy EKS Cluster**: Use the general use subnets for EKS node groups
2. **Setup Transit Gateway**: Attach the TGW subnets to your Transit Gateway
3. **Configure Security Groups**: Customize security groups based on your requirements
4. **Add NAT Gateways**: If you need private subnets with internet access

## Security Considerations

- The general use subnets are configured as public subnets by default
- Security group allows HTTP/HTTPS from anywhere and SSH from within VPC
- Modify security group rules based on your security requirements
- Consider using private subnets for sensitive workloads

## Support

For issues or questions, please refer to the Terraform AWS Provider documentation or create an issue in your project repository.
