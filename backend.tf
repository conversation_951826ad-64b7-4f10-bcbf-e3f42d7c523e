# S3 Bucket for Terraform State Storage
resource "aws_s3_bucket" "terraform_state" {
  bucket        = "fresh-eks-infra-terraform-state-ireland-dev"
  force_destroy = false

  tags = merge(
    var.common_tags,
    {
      Name        = "TerraformStateStorage"
      Description = "S3 bucket for storing Terraform state files"
      Purpose     = "Infrastructure"
    }
  )

  lifecycle {
    prevent_destroy = true
  }
}

# S3 Bucket Versioning Configuration
resource "aws_s3_bucket_versioning" "terraform_state_versioning" {
  bucket = aws_s3_bucket.terraform_state.id

  versioning_configuration {
    status = "Enabled"
  }

  depends_on = [aws_s3_bucket.terraform_state]
}

# S3 Bucket Server-Side Encryption Configuration
resource "aws_s3_bucket_server_side_encryption_configuration" "default" {
  bucket = aws_s3_bucket.terraform_state.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }

  depends_on = [aws_s3_bucket.terraform_state]
}

# S3 Bucket Public Access Block (Security Best Practice)
resource "aws_s3_bucket_public_access_block" "terraform_state" {
  bucket = aws_s3_bucket.terraform_state.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true

  depends_on = [aws_s3_bucket.terraform_state]
}

# DynamoDB Table for Terraform State Locking
resource "aws_dynamodb_table" "terraform_state_lock" {
  name         = var.dynamodb_table_name
  billing_mode = var.billing_mode
  hash_key     = "LockID"

  attribute {
    name = "LockID"
    type = "S"
  }

  point_in_time_recovery {
    enabled = true
  }

  server_side_encryption {
    enabled = true
  }

  tags = merge(
    var.common_tags,
    {
      Name        = "TerraformStateLock"
      Description = "DynamoDB table for Terraform state locking"
      Purpose     = "Infrastructure"
    }
  )

  lifecycle {
    prevent_destroy = false
  }
}
