# PowerShell script to destroy VPC infrastructure while preserving S3 backend
# This script targets only the VPC resources and excludes the S3 backend infrastructure

Write-Host "Starting VPC-only destruction (preserving S3 backend)..." -ForegroundColor Green

# Define the resources to destroy (VPC infrastructure only)
$vpcResources = @(
    "aws_vpc.main",
    "aws_subnet.private_eks_az1",
    "aws_subnet.private_eks_az2", 
    "aws_subnet.private_lb_az1",
    "aws_subnet.private_lb_az2",
    "aws_subnet.intra_az1",
    "aws_subnet.intra_az2",
    "aws_subnet.tgw_attachment_1a",
    "aws_subnet.tgw_attachment_1b",
    "aws_ec2_transit_gateway_vpc_attachment.main",
    "aws_internet_gateway.main",
    "aws_security_group.general_use",
    "aws_route_table.private_eks_az1",
    "aws_route_table.private_eks_az2",
    "aws_route_table.private_lb_az1", 
    "aws_route_table.private_lb_az2",
    "aws_route_table.intra_az1",
    "aws_route_table.intra_az2",
    "aws_route_table.tgw_attachment_1a",
    "aws_route_table.tgw_attachment_1b"
)

# Build the terraform destroy command with all target resources
$targetArgs = $vpcResources | ForEach-Object { "-target=`"$_`"" }
$destroyCommand = "terraform destroy " + ($targetArgs -join " ")

Write-Host "Resources to be destroyed:" -ForegroundColor Yellow
$vpcResources | ForEach-Object { Write-Host "  - $_" -ForegroundColor Cyan }

Write-Host "`nResources that will be PRESERVED:" -ForegroundColor Yellow
Write-Host "  - aws_s3_bucket.terraform_state" -ForegroundColor Green
Write-Host "  - aws_s3_bucket_versioning.terraform_state_versioning" -ForegroundColor Green
Write-Host "  - aws_s3_bucket_server_side_encryption_configuration.default" -ForegroundColor Green
Write-Host "  - aws_s3_bucket_public_access_block.terraform_state" -ForegroundColor Green
Write-Host "  - aws_dynamodb_table.terraform_state_lock" -ForegroundColor Green

Write-Host "`nWARNING: This will destroy all VPC infrastructure!" -ForegroundColor Red
$confirmation = Read-Host "Do you want to continue? (yes/no)"

if ($confirmation -eq "yes") {
    Write-Host "`nExecuting targeted destroy..." -ForegroundColor Red
    Invoke-Expression $destroyCommand

    if ($LASTEXITCODE -eq 0) {
        Write-Host "`nVPC infrastructure destroyed successfully!" -ForegroundColor Green
        Write-Host "S3 backend infrastructure preserved." -ForegroundColor Green
        Write-Host "You can redeploy anytime with: terraform apply" -ForegroundColor Cyan
    } else {
        Write-Host "`nDestroy operation failed!" -ForegroundColor Red
    }
} else {
    Write-Host "`nOperation cancelled." -ForegroundColor Yellow
}
