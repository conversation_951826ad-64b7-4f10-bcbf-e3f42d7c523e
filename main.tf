# Configure the AWS Provider
terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  region = var.aws_region
}

# Data source to get available AZs
data "aws_availability_zones" "available" {
  state = "available"
}

# Create VPC
resource "aws_vpc" "main" {
  cidr_block           = var.vpc_cidr
  enable_dns_hostnames = true
  enable_dns_support   = true

  tags = {
    Name        = "${var.project_name}-vpc"
    Environment = var.environment
    Project     = var.project_name
  }
}

# Internet Gateway
resource "aws_internet_gateway" "main" {
  vpc_id = aws_vpc.main.id

  tags = {
    Name        = "${var.project_name}-igw"
    Environment = var.environment
    Project     = var.project_name
  }
}

# Transit Gateway Attachment Subnets (2 subnets)
resource "aws_subnet" "tgw_attachment" {
  count = 2

  vpc_id            = aws_vpc.main.id
  cidr_block        = var.tgw_subnet_cidrs[count.index]
  availability_zone = data.aws_availability_zones.available.names[count.index]

  tags = {
    Name        = "${var.project_name}-tgw-subnet-${count.index + 1}"
    Environment = var.environment
    Project     = var.project_name
    Type        = "Transit Gateway Attachment"
  }
}

# General Use Subnets (5 subnets)
resource "aws_subnet" "general_use" {
  count = 5

  vpc_id                  = aws_vpc.main.id
  cidr_block              = var.general_subnet_cidrs[count.index]
  availability_zone       = data.aws_availability_zones.available.names[count.index % length(data.aws_availability_zones.available.names)]
  map_public_ip_on_launch = var.map_public_ip_on_launch

  tags = {
    Name        = "${var.project_name}-general-subnet-${count.index + 1}"
    Environment = var.environment
    Project     = var.project_name
    Type        = "General Use"
  }
}


resource "aws_route_table" "general_use" {
  vpc_id = aws_vpc.main.id

  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = aws_internet_gateway.main.id
  }

  tags = {
    Name        = "${var.project_name}-general-rt"
    Environment = var.environment
    Project     = var.project_name
  }
}

# Route Table for TGW Attachment Subnets
resource "aws_route_table" "tgw_attachment" {
  vpc_id = aws_vpc.main.id

  tags = {
    Name        = "${var.project_name}-tgw-rt"
    Environment = var.environment
    Project     = var.project_name
  }
}

# Associate General Use Subnets with Route Table
resource "aws_route_table_association" "general_use" {
  count = 5

  subnet_id      = aws_subnet.general_use[count.index].id
  route_table_id = aws_route_table.general_use.id
}

# Associate TGW Attachment Subnets with Route Table
resource "aws_route_table_association" "tgw_attachment" {
  count = 2

  subnet_id      = aws_subnet.tgw_attachment[count.index].id
  route_table_id = aws_route_table.tgw_attachment.id
}

# Security Group for General Use
resource "aws_security_group" "general_use" {
  name_prefix = "${var.project_name}-general-"
  vpc_id      = aws_vpc.main.id

  ingress {
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = [var.vpc_cidr]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name        = "${var.project_name}-general-sg"
    Environment = var.environment
    Project     = var.project_name
  }
}
