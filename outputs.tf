# VPC Outputs
output "vpc_id" {
  description = "ID of the VPC"
  value       = aws_vpc.main.id
}

output "vpc_cidr_block" {
  description = "CIDR block of the VPC"
  value       = aws_vpc.main.cidr_block
}

output "vpc_arn" {
  description = "ARN of the VPC"
  value       = aws_vpc.main.arn
}

# Internet Gateway Output
output "internet_gateway_id" {
  description = "ID of the Internet Gateway"
  value       = aws_internet_gateway.main.id
}

# Transit Gateway Attachment Subnet Outputs
output "tgw_attachment_subnet_ids" {
  description = "IDs of the Transit Gateway attachment subnets"
  value       = aws_subnet.tgw_attachment[*].id
}

output "tgw_attachment_subnet_cidrs" {
  description = "CIDR blocks of the Transit Gateway attachment subnets"
  value       = aws_subnet.tgw_attachment[*].cidr_block
}

output "tgw_attachment_subnet_azs" {
  description = "Availability zones of the Transit Gateway attachment subnets"
  value       = aws_subnet.tgw_attachment[*].availability_zone
}

# General Use Subnet Outputs
output "general_use_subnet_ids" {
  description = "IDs of the general use subnets"
  value       = aws_subnet.general_use[*].id
}

output "general_use_subnet_cidrs" {
  description = "CIDR blocks of the general use subnets"
  value       = aws_subnet.general_use[*].cidr_block
}

output "general_use_subnet_azs" {
  description = "Availability zones of the general use subnets"
  value       = aws_subnet.general_use[*].availability_zone
}

# Route Table Outputs
output "general_use_route_table_id" {
  description = "ID of the general use route table"
  value       = aws_route_table.general_use.id
}

output "tgw_attachment_route_table_id" {
  description = "ID of the TGW attachment route table"
  value       = aws_route_table.tgw_attachment.id
}

# Security Group Output
output "general_use_security_group_id" {
  description = "ID of the general use security group"
  value       = aws_security_group.general_use.id
}

# All Subnet IDs (combined)
output "all_subnet_ids" {
  description = "All subnet IDs (TGW + General Use)"
  value       = concat(aws_subnet.tgw_attachment[*].id, aws_subnet.general_use[*].id)
}

# Availability Zones
output "availability_zones" {
  description = "List of availability zones used"
  value       = data.aws_availability_zones.available.names
}

# S3 Backend Resources
output "terraform_state_bucket_id" {
  description = "ID of the S3 bucket for Terraform state"
  value       = aws_s3_bucket.terraform_state.id
}

output "terraform_state_bucket_arn" {
  description = "ARN of the S3 bucket for Terraform state"
  value       = aws_s3_bucket.terraform_state.arn
}

output "terraform_state_bucket_region" {
  description = "Region of the S3 bucket for Terraform state"
  value       = aws_s3_bucket.terraform_state.region
}

# DynamoDB State Lock Table
output "dynamodb_table_name" {
  description = "Name of the DynamoDB table for state locking"
  value       = aws_dynamodb_table.terraform_state_lock.name
}

output "dynamodb_table_arn" {
  description = "ARN of the DynamoDB table for state locking"
  value       = aws_dynamodb_table.terraform_state_lock.arn
}

# Transit Gateway Outputs
output "transit_gateway_id" {
  description = "ID of the Transit Gateway"
  value       = var.create_transit_gateway ? aws_ec2_transit_gateway.main[0].id : null
}

output "transit_gateway_arn" {
  description = "ARN of the Transit Gateway"
  value       = var.create_transit_gateway ? aws_ec2_transit_gateway.main[0].arn : null
}

output "transit_gateway_association_default_route_table_id" {
  description = "ID of the default association route table"
  value       = var.create_transit_gateway ? aws_ec2_transit_gateway.main[0].association_default_route_table_id : null
}

output "transit_gateway_propagation_default_route_table_id" {
  description = "ID of the default propagation route table"
  value       = var.create_transit_gateway ? aws_ec2_transit_gateway.main[0].propagation_default_route_table_id : null
}

output "transit_gateway_vpc_attachment_id" {
  description = "ID of the Transit Gateway VPC attachment"
  value       = var.create_transit_gateway ? aws_ec2_transit_gateway_vpc_attachment.main[0].id : null
}

output "transit_gateway_vpc_attachment_vpc_owner_id" {
  description = "ID of the AWS account that owns the VPC"
  value       = var.create_transit_gateway ? aws_ec2_transit_gateway_vpc_attachment.main[0].vpc_owner_id : null
}
