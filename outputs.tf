# VPC Outputs
output "vpc_id" {
  description = "ID of the VPC"
  value       = aws_vpc.main.id
}

output "vpc_cidr_block" {
  description = "CIDR block of the VPC"
  value       = aws_vpc.main.cidr_block
}

output "vpc_arn" {
  description = "ARN of the VPC"
  value       = aws_vpc.main.arn
}

# Internet Gateway Output
output "internet_gateway_id" {
  description = "ID of the Internet Gateway"
  value       = aws_internet_gateway.main.id
}

# Transit Gateway Attachment Subnet Outputs
output "tgw_attachment_subnet_ids" {
  description = "IDs of the Transit Gateway attachment subnets"
  value       = [
    aws_subnet.tgw_attachment_1a.id,
    aws_subnet.tgw_attachment_1b.id
  ]
}

output "tgw_attachment_subnet_cidrs" {
  description = "CIDR blocks of the Transit Gateway attachment subnets"
  value       = [
    aws_subnet.tgw_attachment_1a.cidr_block,
    aws_subnet.tgw_attachment_1b.cidr_block
  ]
}

output "tgw_attachment_subnet_azs" {
  description = "Availability zones of the Transit Gateway attachment subnets"
  value       = [
    aws_subnet.tgw_attachment_1a.availability_zone,
    aws_subnet.tgw_attachment_1b.availability_zone
  ]
}

# Individual TGW Subnet Outputs
output "tgw_subnet_1a_id" {
  description = "ID of TGW-ECOMM-DEV-1a subnet"
  value       = aws_subnet.tgw_attachment_1a.id
}

output "tgw_subnet_1b_id" {
  description = "ID of TGW-ECOMM-DEV-1b subnet"
  value       = aws_subnet.tgw_attachment_1b.id
}

output "tgw_route_table_1a_id" {
  description = "ID of TGW-ECOMM-DEV-1a route table"
  value       = aws_route_table.tgw_attachment_1a.id
}

output "tgw_route_table_1b_id" {
  description = "ID of TGW-ECOMM-DEV-1b route table"
  value       = aws_route_table.tgw_attachment_1b.id
}

# Private EKS Subnet Outputs
output "private_eks_subnet_ids" {
  description = "IDs of the private EKS subnets"
  value = {
    az1 = aws_subnet.private_eks_az1.id
    az2 = aws_subnet.private_eks_az2.id
  }
}

output "private_eks_subnet_cidrs" {
  description = "CIDR blocks of the private EKS subnets"
  value = {
    az1 = aws_subnet.private_eks_az1.cidr_block
    az2 = aws_subnet.private_eks_az2.cidr_block
  }
}

# Private Load Balancer Subnet Outputs
output "private_lb_subnet_ids" {
  description = "IDs of the private load balancer subnets"
  value = {
    az1 = aws_subnet.private_lb_az1.id
    az2 = aws_subnet.private_lb_az2.id
  }
}

output "private_lb_subnet_cidrs" {
  description = "CIDR blocks of the private load balancer subnets"
  value = {
    az1 = aws_subnet.private_lb_az1.cidr_block
    az2 = aws_subnet.private_lb_az2.cidr_block
  }
}

# Intra (Isolated) Subnet Outputs
output "intra_subnet_ids" {
  description = "IDs of the intra (isolated) subnets"
  value = {
    az1 = aws_subnet.intra_az1.id
    az2 = aws_subnet.intra_az2.id
  }
}

output "intra_subnet_cidrs" {
  description = "CIDR blocks of the intra (isolated) subnets"
  value = {
    az1 = aws_subnet.intra_az1.cidr_block
    az2 = aws_subnet.intra_az2.cidr_block
  }
}

# Route Table Outputs
output "private_eks_route_table_ids" {
  description = "IDs of the private EKS route tables"
  value = {
    az1 = aws_route_table.private_eks_az1.id
    az2 = aws_route_table.private_eks_az2.id
  }
}

output "private_lb_route_table_ids" {
  description = "IDs of the private load balancer route tables"
  value = {
    az1 = aws_route_table.private_lb_az1.id
    az2 = aws_route_table.private_lb_az2.id
  }
}

output "intra_route_table_ids" {
  description = "IDs of the intra (isolated) route tables"
  value = {
    az1 = aws_route_table.intra_az1.id
    az2 = aws_route_table.intra_az2.id
  }
}

output "tgw_attachment_route_table_ids" {
  description = "IDs of the TGW attachment route tables"
  value       = [
    aws_route_table.tgw_attachment_1a.id,
    aws_route_table.tgw_attachment_1b.id
  ]
}

# Security Group Output
output "general_use_security_group_id" {
  description = "ID of the general use security group"
  value       = aws_security_group.general_use.id
}

# All Subnet IDs (combined)
output "all_subnet_ids" {
  description = "All subnet IDs (TGW + Private EKS + Private LB + Intra)"
  value       = [
    aws_subnet.tgw_attachment_1a.id,
    aws_subnet.tgw_attachment_1b.id,
    aws_subnet.private_eks_az1.id,
    aws_subnet.private_eks_az2.id,
    aws_subnet.private_lb_az1.id,
    aws_subnet.private_lb_az2.id,
    aws_subnet.intra_az1.id,
    aws_subnet.intra_az2.id
  ]
}

# Availability Zones
output "availability_zones" {
  description = "List of availability zones used"
  value       = data.aws_availability_zones.available.names
}

# S3 Backend Resources
output "terraform_state_bucket_id" {
  description = "ID of the S3 bucket for Terraform state"
  value       = aws_s3_bucket.terraform_state.id
}

output "terraform_state_bucket_arn" {
  description = "ARN of the S3 bucket for Terraform state"
  value       = aws_s3_bucket.terraform_state.arn
}

output "terraform_state_bucket_region" {
  description = "Region of the S3 bucket for Terraform state"
  value       = aws_s3_bucket.terraform_state.region
}

# DynamoDB State Lock Table
output "dynamodb_table_name" {
  description = "Name of the DynamoDB table for state locking"
  value       = aws_dynamodb_table.terraform_state_lock.name
}

output "dynamodb_table_arn" {
  description = "ARN of the DynamoDB table for state locking"
  value       = aws_dynamodb_table.terraform_state_lock.arn
}

# Transit Gateway Outputs (for existing TGW)
output "existing_transit_gateway_id" {
  description = "ID of the existing Transit Gateway being used"
  value       = var.existing_transit_gateway_id
}

output "transit_gateway_vpc_attachment_id" {
  description = "ID of the Transit Gateway VPC attachment"
  value       = var.create_transit_gateway_attachment && var.existing_transit_gateway_id != null ? aws_ec2_transit_gateway_vpc_attachment.main[0].id : null
}

output "transit_gateway_vpc_attachment_state" {
  description = "State of the Transit Gateway VPC attachment"
  value       = var.create_transit_gateway_attachment && var.existing_transit_gateway_id != null ? "available" : null
}

output "transit_gateway_vpc_attachment_vpc_owner_id" {
  description = "ID of the AWS account that owns the VPC"
  value       = var.create_transit_gateway_attachment && var.existing_transit_gateway_id != null ? aws_ec2_transit_gateway_vpc_attachment.main[0].vpc_owner_id : null
}


