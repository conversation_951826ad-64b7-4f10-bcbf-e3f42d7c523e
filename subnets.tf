# General Use Subnets (5 subnets)
resource "aws_subnet" "general_use" {
  count = 5

  vpc_id                  = aws_vpc.main.id
  cidr_block              = var.general_subnet_cidrs[count.index]
  availability_zone       = data.aws_availability_zones.available.names[count.index % length(data.aws_availability_zones.available.names)]
  map_public_ip_on_launch = var.map_public_ip_on_launch

  tags = {
    Name        = "${var.project_name}-general-subnet-${count.index + 1}"
    Environment = var.environment
    Project     = var.project_name
    Type        = "General Use"
  }
}

# Route Table for General Use Subnets
resource "aws_route_table" "general_use" {
  vpc_id = aws_vpc.main.id

  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = aws_internet_gateway.main.id
  }

  tags = {
    Name        = "${var.project_name}-general-rt"
    Environment = var.environment
    Project     = var.project_name
  }
}

# Associate General Use Subnets with Route Table
resource "aws_route_table_association" "general_use" {
  count = 5

  subnet_id      = aws_subnet.general_use[count.index].id
  route_table_id = aws_route_table.general_use.id
}
