# Private EKS Subnets
resource "aws_subnet" "private_eks_az1" {
  vpc_id                  = aws_vpc.main.id
  cidr_block              = var.private_eks_subnet_cidrs.az1
  availability_zone       = "eu-west-1a"
  map_public_ip_on_launch = false

  tags = merge(
    var.common_tags,
    {
      Name                     = "private-eks-az1"
      Environment              = var.environment
      Project                  = var.project_name
      Type                     = "Private EKS"
      AZ                       = "eu-west-1a"
      "kubernetes.io/role/internal-elb" = "1"
    }
  )
}

resource "aws_subnet" "private_eks_az2" {
  vpc_id                  = aws_vpc.main.id
  cidr_block              = var.private_eks_subnet_cidrs.az2
  availability_zone       = "eu-west-1c"
  map_public_ip_on_launch = false

  tags = merge(
    var.common_tags,
    {
      Name                     = "private-eks-az2"
      Environment              = var.environment
      Project                  = var.project_name
      Type                     = "Private EKS"
      AZ                       = "eu-west-1c"
      "kubernetes.io/role/internal-elb" = "1"
    }
  )
}

# Private Load Balancer Subnets
resource "aws_subnet" "private_lb_az1" {
  vpc_id                  = aws_vpc.main.id
  cidr_block              = var.private_lb_subnet_cidrs.az1
  availability_zone       = "eu-west-1a"
  map_public_ip_on_launch = false

  tags = merge(
    var.common_tags,
    {
      Name                     = "private-lb-az1"
      Environment              = var.environment
      Project                  = var.project_name
      Type                     = "Private Load Balancer"
      AZ                       = "eu-west-1a"
      "kubernetes.io/role/internal-elb" = "1"
    }
  )
}

resource "aws_subnet" "private_lb_az2" {
  vpc_id                  = aws_vpc.main.id
  cidr_block              = var.private_lb_subnet_cidrs.az2
  availability_zone       = "eu-west-1c"
  map_public_ip_on_launch = false

  tags = merge(
    var.common_tags,
    {
      Name                     = "private-lb-az2"
      Environment              = var.environment
      Project                  = var.project_name
      Type                     = "Private Load Balancer"
      AZ                       = "eu-west-1c"
      "kubernetes.io/role/internal-elb" = "1"
    }
  )
}

# Intra (Isolated) Subnets
resource "aws_subnet" "intra_az1" {
  vpc_id                  = aws_vpc.main.id
  cidr_block              = var.intra_subnet_cidrs.az1
  availability_zone       = "eu-west-1a"
  map_public_ip_on_launch = false

  tags = merge(
    var.common_tags,
    {
      Name        = "intra-az1"
      Environment = var.environment
      Project     = var.project_name
      Type        = "Intra (Isolated)"
      AZ          = "eu-west-1a"
    }
  )
}

resource "aws_subnet" "intra_az2" {
  vpc_id                  = aws_vpc.main.id
  cidr_block              = var.intra_subnet_cidrs.az2
  availability_zone       = "eu-west-1c"
  map_public_ip_on_launch = false

  tags = merge(
    var.common_tags,
    {
      Name        = "intra-az2"
      Environment = var.environment
      Project     = var.project_name
      Type        = "Intra (Isolated)"
      AZ          = "eu-west-1c"
    }
  )
}

# Route Tables for Private EKS Subnets
resource "aws_route_table" "private_eks_az1" {
  vpc_id = aws_vpc.main.id

  tags = merge(
    var.common_tags,
    {
      Name        = "private-eks-az1-rt"
      Environment = var.environment
      Project     = var.project_name
      Type        = "Private EKS Route Table"
      AZ          = "eu-west-1a"
    }
  )
}

resource "aws_route_table" "private_eks_az2" {
  vpc_id = aws_vpc.main.id

  tags = merge(
    var.common_tags,
    {
      Name        = "private-eks-az2-rt"
      Environment = var.environment
      Project     = var.project_name
      Type        = "Private EKS Route Table"
      AZ          = "eu-west-1c"
    }
  )
}

# Route Tables for Private Load Balancer Subnets
resource "aws_route_table" "private_lb_az1" {
  vpc_id = aws_vpc.main.id

  tags = merge(
    var.common_tags,
    {
      Name        = "private-lb-az1-rt"
      Environment = var.environment
      Project     = var.project_name
      Type        = "Private LB Route Table"
      AZ          = "eu-west-1a"
    }
  )
}

resource "aws_route_table" "private_lb_az2" {
  vpc_id = aws_vpc.main.id

  tags = merge(
    var.common_tags,
    {
      Name        = "private-lb-az2-rt"
      Environment = var.environment
      Project     = var.project_name
      Type        = "Private LB Route Table"
      AZ          = "eu-west-1c"
    }
  )
}

# Route Tables for Intra (Isolated) Subnets
resource "aws_route_table" "intra_az1" {
  vpc_id = aws_vpc.main.id

  tags = merge(
    var.common_tags,
    {
      Name        = "intra-az1-rt"
      Environment = var.environment
      Project     = var.project_name
      Type        = "Intra (Isolated) Route Table"
      AZ          = "eu-west-1a"
    }
  )
}

resource "aws_route_table" "intra_az2" {
  vpc_id = aws_vpc.main.id

  tags = merge(
    var.common_tags,
    {
      Name        = "intra-az2-rt"
      Environment = var.environment
      Project     = var.project_name
      Type        = "Intra (Isolated) Route Table"
      AZ          = "eu-west-1c"
    }
  )
}

# Route Table Associations for Private EKS Subnets
resource "aws_route_table_association" "private_eks_az1" {
  subnet_id      = aws_subnet.private_eks_az1.id
  route_table_id = aws_route_table.private_eks_az1.id
}

resource "aws_route_table_association" "private_eks_az2" {
  subnet_id      = aws_subnet.private_eks_az2.id
  route_table_id = aws_route_table.private_eks_az2.id
}

# Route Table Associations for Private Load Balancer Subnets
resource "aws_route_table_association" "private_lb_az1" {
  subnet_id      = aws_subnet.private_lb_az1.id
  route_table_id = aws_route_table.private_lb_az1.id
}

resource "aws_route_table_association" "private_lb_az2" {
  subnet_id      = aws_subnet.private_lb_az2.id
  route_table_id = aws_route_table.private_lb_az2.id
}

# Route Table Associations for Intra (Isolated) Subnets
resource "aws_route_table_association" "intra_az1" {
  subnet_id      = aws_subnet.intra_az1.id
  route_table_id = aws_route_table.intra_az1.id
}

resource "aws_route_table_association" "intra_az2" {
  subnet_id      = aws_subnet.intra_az2.id
  route_table_id = aws_route_table.intra_az2.id
}
