# Example Terraform variables file
# Copy this file to terraform.tfvars and modify as needed

# AWS Configuration
aws_region = "us-west-2"

# Project Configuration
project_name = "eks-main"
environment  = "dev"

# VPC Configuration
vpc_cidr = "**********/16"

# Transit Gateway Attachment Subnets
tgw_subnet_cidrs = [
  "**********/24",  # TGW Subnet 1 - AZ 1
  "**********/24"   # TGW Subnet 2 - AZ 2
]

# General Use Subnets
general_subnet_cidrs = [
  "***********/24", # General Subnet 1 - AZ 1
  "***********/24", # General Subnet 2 - AZ 2
  "***********/24", # General Subnet 3 - AZ 3
  "***********/24", # General Subnet 4 - AZ 1 (cycling back)
  "***********/24"  # General Subnet 5 - AZ 2 (cycling back)
]

# Network Configuration
map_public_ip_on_launch = true

# Additional Tags (optional)
additional_tags = {
  Owner       = "DevOps Team"
  CostCenter  = "Engineering"
  Terraform   = "true"
}
