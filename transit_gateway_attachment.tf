# Transit Gateway Attachment Subnets (2 subnets)
resource "aws_subnet" "tgw_attachment" {
  count = 2

  vpc_id            = aws_vpc.main.id
  cidr_block        = var.tgw_subnet_cidrs[count.index]
  availability_zone = data.aws_availability_zones.available.names[count.index]

  tags = {
    Name        = "${var.project_name}-tgw-subnet-${count.index + 1}"
    Environment = var.environment
    Project     = var.project_name
    Type        = "Transit Gateway Attachment"
  }
}

# Route Table for TGW Attachment Subnets
resource "aws_route_table" "tgw_attachment" {
  vpc_id = aws_vpc.main.id

  tags = {
    Name        = "${var.project_name}-tgw-rt"
    Environment = var.environment
    Project     = var.project_name
  }
}

# Associate TGW Attachment Subnets with Route Table
resource "aws_route_table_association" "tgw_attachment" {
  count = 2

  subnet_id      = aws_subnet.tgw_attachment[count.index].id
  route_table_id = aws_route_table.tgw_attachment.id
}
