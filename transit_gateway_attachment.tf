# Transit Gateway Attachment Subnets (2 subnets)
resource "aws_subnet" "tgw_attachment" {
  count = 2

  vpc_id            = aws_vpc.main.id
  cidr_block        = var.tgw_subnet_cidrs[count.index]
  availability_zone = data.aws_availability_zones.available.names[count.index]

  tags = {
    Name        = "${var.project_name}-tgw-subnet-${count.index + 1}"
    Environment = var.environment
    Project     = var.project_name
    Type        = "Transit Gateway Attachment"
  }
}

# Route Table for TGW Attachment Subnets
resource "aws_route_table" "tgw_attachment" {
  vpc_id = aws_vpc.main.id

  tags = {
    Name        = "${var.project_name}-tgw-rt"
    Environment = var.environment
    Project     = var.project_name
  }
}

# Associate TGW Attachment Subnets with Route Table
resource "aws_route_table_association" "tgw_attachment" {
  count = 2

  subnet_id      = aws_subnet.tgw_attachment[count.index].id
  route_table_id = aws_route_table.tgw_attachment.id
}

# Data source to get existing Transit Gateway information
data "aws_ec2_transit_gateway" "existing" {
  count = var.create_transit_gateway_attachment && var.existing_transit_gateway_id != null ? 1 : 0
  id    = var.existing_transit_gateway_id
}

# Transit Gateway VPC Attachment to existing Transit Gateway
resource "aws_ec2_transit_gateway_vpc_attachment" "main" {
  count = var.create_transit_gateway_attachment && var.existing_transit_gateway_id != null ? 1 : 0

  subnet_ids         = aws_subnet.tgw_attachment[*].id
  transit_gateway_id = var.existing_transit_gateway_id
  vpc_id             = aws_vpc.main.id

  # Configuration options
  dns_support                                     = var.enable_dns_support_attachment ? "enable" : "disable"
  ipv6_support                                   = var.enable_ipv6_support_attachment ? "enable" : "disable"
  appliance_mode_support                         = var.appliance_mode_support

  tags = merge(
    var.common_tags,
    {
      Name                = "${var.project_name}-tgw-vpc-attachment"
      Environment         = var.environment
      Project             = var.project_name
      VPC                 = aws_vpc.main.id
      TransitGatewayId    = var.existing_transit_gateway_id
      AttachmentType      = "VPC"
    }
  )

  depends_on = [
    aws_subnet.tgw_attachment,
    aws_route_table_association.tgw_attachment
  ]
}

# Optional: Add routes to Transit Gateway in the TGW route table
resource "aws_route" "tgw_to_general_subnets" {
  count = var.create_transit_gateway_attachment && var.existing_transit_gateway_id != null ? 1 : 0

  route_table_id         = aws_route_table.tgw_attachment.id
  destination_cidr_block = "0.0.0.0/0"  # Or specify specific CIDR blocks you want to route through TGW
  transit_gateway_id     = var.existing_transit_gateway_id

  depends_on = [aws_ec2_transit_gateway_vpc_attachment.main]
}
