# Transit Gateway Attachment Subnets (2 subnets)
resource "aws_subnet" "tgw_attachment" {
  count = 2

  vpc_id            = aws_vpc.main.id
  cidr_block        = var.tgw_subnet_cidrs[count.index]
  availability_zone = data.aws_availability_zones.available.names[count.index]

  tags = {
    Name        = "${var.project_name}-tgw-subnet-${count.index + 1}"
    Environment = var.environment
    Project     = var.project_name
    Type        = "Transit Gateway Attachment"
  }
}

# Route Table for TGW Attachment Subnets
resource "aws_route_table" "tgw_attachment" {
  vpc_id = aws_vpc.main.id

  tags = {
    Name        = "${var.project_name}-tgw-rt"
    Environment = var.environment
    Project     = var.project_name
  }
}

# Associate TGW Attachment Subnets with Route Table
resource "aws_route_table_association" "tgw_attachment" {
  count = 2

  subnet_id      = aws_subnet.tgw_attachment[count.index].id
  route_table_id = aws_route_table.tgw_attachment.id
}

# Transit Gateway
resource "aws_ec2_transit_gateway" "main" {
  count = var.create_transit_gateway ? 1 : 0

  description                     = var.transit_gateway_description
  amazon_side_asn                 = var.transit_gateway_asn
  auto_accept_shared_attachments  = "enable"
  default_route_table_association = var.default_route_table_association
  default_route_table_propagation = var.default_route_table_propagation
  dns_support                     = var.enable_dns_support ? "enable" : "disable"
  multicast_support              = var.enable_multicast_support ? "enable" : "disable"

  tags = merge(
    var.common_tags,
    {
      Name        = "${var.project_name}-tgw"
      Environment = var.environment
      Project     = var.project_name
      Description = var.transit_gateway_description
    }
  )
}

# Transit Gateway VPC Attachment
resource "aws_ec2_transit_gateway_vpc_attachment" "main" {
  count = var.create_transit_gateway ? 1 : 0

  subnet_ids         = aws_subnet.tgw_attachment[*].id
  transit_gateway_id = aws_ec2_transit_gateway.main[0].id
  vpc_id             = aws_vpc.main.id

  # Enable DNS support for the attachment
  dns_support = var.enable_dns_support ? "enable" : "disable"

  tags = merge(
    var.common_tags,
    {
      Name        = "${var.project_name}-tgw-attachment"
      Environment = var.environment
      Project     = var.project_name
      VPC         = aws_vpc.main.id
    }
  )

  depends_on = [
    aws_ec2_transit_gateway.main,
    aws_subnet.tgw_attachment
  ]
}
