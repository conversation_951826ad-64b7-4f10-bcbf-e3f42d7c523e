# AWS Region
variable "aws_region" {
  description = "AWS region for resources"
  type        = string
  default     = "us-west-2"
}

# Project Name
variable "project_name" {
  description = "EKS-deployment-test"
  type        = string
  default     = "eks-main-test"
}

# Environment
variable "environment" {
  description = "Environment name"
  type        = string
  default     = "dev"
}

# VPC CIDR Block
variable "vpc_cidr" {
  description = "CIDR block for VPC"
  type        = string
  default     = "**********/16"
}

# Transit Gateway Attachment Subnet CIDRs
variable "tgw_subnet_cidrs" {
  description = "CIDR blocks for Transit Gateway attachment subnets"
  type        = list(string)
  default = [
    "**********/26",  # TGW Subnet 1
    "**********/26"   # TGW Subnet 2
  ]
}

# General Use Subnet CIDRs
variable "general_subnet_cidrs" {
  description = "CIDR blocks for general use subnets"
  type        = list(string)
  default = [
    "***********/24", # General Subnet 1
    "***********/24", # General Subnet 2
    "***********/24", # General Subnet 3
    "***********/24", # General Subnet 4
    "***********/24"  # General Subnet 5
  ]
}

# Map Public IP on Launch
variable "map_public_ip_on_launch" {
  description = "Should be false if you do not want to auto-assign public IP on launch"
  type        = bool
  default     = true
}

# Additional tags
variable "additional_tags" {
  description = "Additional tags to apply to all resources"
  type        = map(string)
  default     = {}
}

# IAM User Access Key ID (for local testing)
variable "iam_user_access_key_id" {
  description = "IAM user access key ID for local testing"
  type        = string
  default     = null
  sensitive   = true
}

# IAM User Secret Access Key (for local testing)
variable "iam_user_secret_access_key" {
  description = "IAM user secret access key for local testing"
  type        = string
  default     = null
  sensitive   = true
}

# Common Tags
variable "common_tags" {
  description = "Common tags to apply to all resources"
  type        = map(string)
  default = {
    Terraform   = "true"
    Owner       = "DevOps Team"
    CostCenter  = "Engineering"
  }
}

# DynamoDB Table Name for State Locking
variable "dynamodb_table_name" {
  description = "Name of the DynamoDB table for Terraform state locking"
  type        = string
  default     = "terraform-state-lock"
}

# DynamoDB Billing Mode
variable "billing_mode" {
  description = "Billing mode for DynamoDB table"
  type        = string
  default     = "PAY_PER_REQUEST"
  validation {
    condition     = contains(["PAY_PER_REQUEST", "PROVISIONED"], var.billing_mode)
    error_message = "Billing mode must be either PAY_PER_REQUEST or PROVISIONED."
  }
}

# Transit Gateway Configuration
variable "create_transit_gateway" {
  description = "Whether to create a Transit Gateway"
  type        = bool
  default     = true
}

variable "transit_gateway_description" {
  description = "Description for the Transit Gateway"
  type        = string
  default     = "Transit Gateway for VPC connectivity"
}

variable "transit_gateway_asn" {
  description = "Private Autonomous System Number (ASN) for the Amazon side of a BGP session"
  type        = number
  default     = 64512
  validation {
    condition     = var.transit_gateway_asn >= 64512 && var.transit_gateway_asn <= 65534
    error_message = "Transit Gateway ASN must be between 64512 and 65534."
  }
}

variable "enable_dns_support" {
  description = "Whether DNS support is enabled for the Transit Gateway"
  type        = bool
  default     = true
}

variable "enable_multicast_support" {
  description = "Whether multicast support is enabled for the Transit Gateway"
  type        = bool
  default     = false
}

variable "default_route_table_association" {
  description = "Whether resource attachments are automatically associated with the default association route table"
  type        = string
  default     = "enable"
  validation {
    condition     = contains(["enable", "disable"], var.default_route_table_association)
    error_message = "Default route table association must be either 'enable' or 'disable'."
  }
}

variable "default_route_table_propagation" {
  description = "Whether resource attachments automatically propagate routes to the default propagation route table"
  type        = string
  default     = "enable"
  validation {
    condition     = contains(["enable", "disable"], var.default_route_table_propagation)
    error_message = "Default route table propagation must be either 'enable' or 'disable'."
  }
}
