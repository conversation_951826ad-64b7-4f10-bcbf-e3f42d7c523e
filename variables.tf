# AWS Region
variable "aws_region" {
  description = "AWS region for resources"
  type        = string
  default     = "eu-west-1"
}

# Project Name
variable "project_name" {
  description = "EKS-deployment-test"
  type        = string
  default     = "eks-main-test"
}

# Environment
variable "environment" {
  description = "Environment name"
  type        = string
  default     = "dev"
}

# VPC CIDR Block
variable "vpc_cidr" {
  description = "CIDR block for VPC"
  type        = string
  default     = "**********/16"
}

# Transit Gateway Attachment Subnet CIDRs
variable "tgw_subnet_cidrs" {
  description = "CIDR blocks for Transit Gateway attachment subnets"
  type        = list(string)
  default = [
    "**********/26",  # TGW Subnet 1
    "**********/26"   # TGW Subnet 2
  ]
}

# Private EKS Subnet CIDRs
variable "private_eks_subnet_cidrs" {
  description = "CIDR blocks for private EKS subnets"
  type        = map(string)
  default = {
    az1 = "***********/24"  
    az2 = "***********/24"  
  }
}

# Private Load Balancer Subnet CIDRs
variable "private_lb_subnet_cidrs" {
  description = "CIDR blocks for private load balancer subnets"
  type        = map(string)
  default = {
    az1 = "***********/24" 
    az2 = "***********/24" 
  }
}

# Intra (Isolated) Subnet CIDRs
variable "intra_subnet_cidrs" {
  description = "CIDR blocks for intra (isolated) subnets"
  type        = map(string)
  default = {
    az1 = "***********/24" 
    az2 = "***********/24"  
  }
}

# Map Public IP on Launch
variable "map_public_ip_on_launch" {
  description = "Should be false if you do not want to auto-assign public IP on launch"
  type        = bool
  default     = true
}

# Additional tags
variable "additional_tags" {
  description = "Additional tags to apply to all resources"
  type        = map(string)
  default     = {}
}

# IAM User Access Key ID (for local testing)
variable "iam_user_access_key_id" {
  description = "IAM user access key ID for local testing"
  type        = string
  default     = null
  sensitive   = true
}

# IAM User Secret Access Key (for local testing)
variable "iam_user_secret_access_key" {
  description = "IAM user secret access key for local testing"
  type        = string
  default     = null
  sensitive   = true
}

# Common Tags
variable "common_tags" {
  description = "Common tags to apply to all resources"
  type        = map(string)
  default = {
    Terraform   = "true"
    Owner       = "DevOps Team"
    CostCenter  = "Engineering"
  }
}

# DynamoDB Table Name for State Locking
variable "dynamodb_table_name" {
  description = "Name of the DynamoDB table for Terraform state locking"
  type        = string
  default     = "terraform-state-lock"
}

# DynamoDB Billing Mode
variable "billing_mode" {
  description = "Billing mode for DynamoDB table"
  type        = string
  default     = "PAY_PER_REQUEST"
  validation {
    condition     = contains(["PAY_PER_REQUEST", "PROVISIONED"], var.billing_mode)
    error_message = "Billing mode must be either PAY_PER_REQUEST or PROVISIONED."
  }
}

# Transit Gateway Configuration
variable "existing_transit_gateway_id" {
  description = "ID of existing Transit Gateway to attach VPC to (if not creating new one)"
  type        = string
  default     = null
}

variable "create_transit_gateway_attachment" {
  description = "Whether to create a Transit Gateway VPC attachment"
  type        = bool
  default     = true
}

variable "enable_dns_support_attachment" {
  description = "Whether DNS support is enabled for the Transit Gateway VPC attachment"
  type        = bool
  default     = true
}

variable "enable_ipv6_support_attachment" {
  description = "Whether IPv6 support is enabled for the Transit Gateway VPC attachment"
  type        = bool
  default     = false
}

variable "appliance_mode_support" {
  description = "Whether Appliance Mode support is enabled for the Transit Gateway VPC attachment"
  type        = string
  default     = "disable"
  validation {
    condition     = contains(["enable", "disable"], var.appliance_mode_support)
    error_message = "Appliance mode support must be either 'enable' or 'disable'."
  }
}
