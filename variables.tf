# AWS Region
variable "aws_region" {
  description = "AWS region for resources"
  type        = string
  default     = "us-west-2"
}

# Project Name
variable "project_name" {
  description = "Name of the project"
  type        = string
  default     = "eks-main"
}

# Environment
variable "environment" {
  description = "Environment name"
  type        = string
  default     = "dev"
}

# VPC CIDR Block
variable "vpc_cidr" {
  description = "CIDR block for VPC"
  type        = string
  default     = "**********/16"
}

# Transit Gateway Attachment Subnet CIDRs
variable "tgw_subnet_cidrs" {
  description = "CIDR blocks for Transit Gateway attachment subnets"
  type        = list(string)
  default = [
    "**********/24",  # TGW Subnet 1
    "**********/24"   # TGW Subnet 2
  ]
}

# General Use Subnet CIDRs
variable "general_subnet_cidrs" {
  description = "CIDR blocks for general use subnets"
  type        = list(string)
  default = [
    "***********/24", # General Subnet 1
    "***********/24", # General Subnet 2
    "***********/24", # General Subnet 3
    "***********/24", # General Subnet 4
    "***********/24"  # General Subnet 5
  ]
}

# Map Public IP on Launch
variable "map_public_ip_on_launch" {
  description = "Should be false if you do not want to auto-assign public IP on launch"
  type        = bool
  default     = true
}

# Additional tags
variable "additional_tags" {
  description = "Additional tags to apply to all resources"
  type        = map(string)
  default     = {}
}
